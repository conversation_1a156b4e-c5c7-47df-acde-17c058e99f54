{% doc %}
  @prompt
    Premium Lifestyle Store Navigation Header - State-of-the-Art Design
    
    NAVIGATION STRUCTURE (5-7 Main Sections):
    - Home
    - Personal Care (Skincare, Hair Care, Body & Bath, Grooming & Wellness)
    - Home Decor (Lighting & Ambience, Organizers & Accents, Tableware & Linens)
    - Daily Accessories (Utility Tools, Travel & Lifestyle, Fashion Accessories)
    - DIY & Tools (Kits & Supplies, Creative/Seasonal, Home Improvement)
    - Gifting (Gift Sets, Gift Cards, Occasion Picks)
    - Sale/Discover (New Arrivals, Seasonal, Editor's Picks)
    
    MEGA MENU FEATURES:
    - Multi-column dropdowns with product images and trust signals
    - Quick filter badges (New, Best Seller, Eco-Friendly, Trending)
    - Micro-testimonials and social proof
    - Seasonal/event promotions
    - Shop by room/occasion filters
    
    UTILITY ICONS (Right-aligned):
    - Search with predictive suggestions
    - Account (sign in/orders)
    - Wishlist (save-for-later)
    - Cart with animated counter
    - Currency & language selector
    
    DESIGN PRINCIPLES:
    - Glassmorphism on scroll with translucent background
    - Gold (#D4AF37) accents on deep charcoal/navy base
    - Premium typography (Playfair Display logo, modern sans-serif nav)
    - Neuromarketing elements and trust signals
    - World-class accessibility and performance

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .luxury-header-{{ ai_gen_id }} {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: linear-gradient(135deg, {{ block.settings.header_background }} 0%, {{ block.settings.header_background | color_lighten: 2 }} 100%);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08), 0 8px 32px rgba(0, 0, 0, 0.04);
    max-height: 20vh;
    height: {{ block.settings.header_height }}px;
    border-bottom: 1px solid rgba({{ block.settings.accent_color | color_extract: 'red' }}, {{ block.settings.accent_color | color_extract: 'green' }}, {{ block.settings.accent_color | color_extract: 'blue' }}, 0.12);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }

  .luxury-header-{{ ai_gen_id }}.scrolled {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(30px);
    -webkit-backdrop-filter: blur(30px);
    height: {{ block.settings.header_height | times: 0.85 }}px;
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.12), 0 8px 60px rgba(0, 0, 0, 0.06);
    border-bottom: 1px solid rgba({{ block.settings.accent_color | color_extract: 'red' }}, {{ block.settings.accent_color | color_extract: 'green' }}, {{ block.settings.accent_color | color_extract: 'blue' }}, 0.2);
  }

  .luxury-header-container-{{ ai_gen_id }} {
    max-width: {{ block.settings.container_width }}px;
    margin: 0 auto;
    padding: 0 {{ block.settings.container_padding }}px;
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    align-items: center;
    height: 100%;
    position: relative;
  }

  .luxury-logo-{{ ai_gen_id }} {
    justify-self: center;
    grid-column: 2;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 10;
  }

  .luxury-header-{{ ai_gen_id }}.scrolled .luxury-logo-{{ ai_gen_id }} {
    transform: scale(0.92);
  }

  .luxury-logo-{{ ai_gen_id }} a {
    text-decoration: none;
    color: {{ block.settings.primary_color }};
    font-family: {{ block.settings.logo_font.family }}, 'Playfair Display', serif;
    font-size: {{ block.settings.logo_size }}px;
    font-weight: {{ block.settings.logo_font.weight | default: 400 }};
    letter-spacing: {{ block.settings.logo_letter_spacing }}px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: block;
    position: relative;
  }

  .luxury-logo-{{ ai_gen_id }} a::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, {{ block.settings.accent_color }}, {{ block.settings.accent_color | color_lighten: 15 }});
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateX(-50%);
  }

  .luxury-logo-{{ ai_gen_id }} a:hover {
    color: {{ block.settings.accent_color }};
    transform: translateY(-1px);
  }

  .luxury-logo-{{ ai_gen_id }} a:hover::after {
    width: 100%;
  }

  .luxury-logo-{{ ai_gen_id }} img {
    max-height: {{ block.settings.logo_height }}px;
    width: auto;
    transition: all 0.3s ease;
  }

  .luxury-header-{{ ai_gen_id }}.scrolled .luxury-logo-{{ ai_gen_id }} img {
    max-height: {{ block.settings.logo_height | times: 0.8 }}px;
  }

  .luxury-nav-{{ ai_gen_id }} {
    justify-self: start;
    grid-column: 1;
    position: relative;
  }

  .luxury-nav-list-{{ ai_gen_id }} {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: {{ block.settings.nav_spacing }}px;
    align-items: center;
  }

  .luxury-nav-item-{{ ai_gen_id }} {
    position: relative;
  }

  .luxury-nav-link-{{ ai_gen_id }} {
    display: flex;
    align-items: center;
    padding: {{ block.settings.nav_padding }}px {{ block.settings.nav_padding | times: 1.5 }}px;
    text-decoration: none;
    color: {{ block.settings.primary_color }};
    font-family: 'Montserrat', 'Poppins', sans-serif;
    font-size: {{ block.settings.nav_font_size }}px;
    font-weight: {{ block.settings.nav_font_weight }};
    letter-spacing: {{ block.settings.nav_letter_spacing }}px;
    text-transform: uppercase;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    min-height: 44px;
    overflow: hidden;
    border-radius: 6px;
  }

  .luxury-nav-link-{{ ai_gen_id }}::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 3px;
    background: linear-gradient(90deg, {{ block.settings.accent_color }}, {{ block.settings.accent_color | color_lighten: 20 }});
    transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 2px;
  }

  .luxury-nav-link-{{ ai_gen_id }}::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba({{ block.settings.accent_color | color_extract: 'red' }}, {{ block.settings.accent_color | color_extract: 'green' }}, {{ block.settings.accent_color | color_extract: 'blue' }}, 0.08), transparent);
    transition: left 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 6px;
  }

  .luxury-nav-link-{{ ai_gen_id }}:hover {
    color: {{ block.settings.accent_color }};
    transform: translateY(-1px);
    background: rgba({{ block.settings.accent_color | color_extract: 'red' }}, {{ block.settings.accent_color | color_extract: 'green' }}, {{ block.settings.accent_color | color_extract: 'blue' }}, 0.05);
  }

  .luxury-nav-link-{{ ai_gen_id }}:hover::before {
    width: 100%;
  }

  .luxury-nav-link-{{ ai_gen_id }}:hover::after {
    left: 100%;
  }

  /* Mega Menu Styles */
  .luxury-mega-menu-{{ ai_gen_id }} {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(30px);
    -webkit-backdrop-filter: blur(30px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15), 0 8px 32px rgba(0, 0, 0, 0.08);
    border-radius: 0 0 16px 16px;
    padding: 40px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-20px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 100;
    border: 1px solid rgba({{ block.settings.accent_color | color_extract: 'red' }}, {{ block.settings.accent_color | color_extract: 'green' }}, {{ block.settings.accent_color | color_extract: 'blue' }}, 0.1);
    max-width: 1200px;
    margin: 0 auto;
  }

  .luxury-nav-item-{{ ai_gen_id }}:hover .luxury-mega-menu-{{ ai_gen_id }} {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }

  .luxury-mega-grid-{{ ai_gen_id }} {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 40px;
    margin-bottom: 30px;
  }

  .luxury-mega-column-{{ ai_gen_id }} {
    position: relative;
  }

  .luxury-mega-title-{{ ai_gen_id }} {
    font-size: 16px;
    font-weight: 600;
    color: {{ block.settings.primary_color }};
    margin-bottom: 20px;
    padding-bottom: 8px;
    border-bottom: 2px solid {{ block.settings.accent_color }};
    text-transform: uppercase;
    letter-spacing: 1px;
  }

  .luxury-mega-link-{{ ai_gen_id }} {
    display: flex;
    align-items: center;
    padding: 12px 0;
    color: {{ block.settings.primary_color | color_lighten: 15 }};
    text-decoration: none;
    font-size: 14px;
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba({{ block.settings.primary_color | color_extract: 'red' }}, {{ block.settings.primary_color | color_extract: 'green' }}, {{ block.settings.primary_color | color_extract: 'blue' }}, 0.08);
  }

  .luxury-mega-link-{{ ai_gen_id }}:hover {
    color: {{ block.settings.accent_color }};
    padding-left: 10px;
    background: rgba({{ block.settings.accent_color | color_extract: 'red' }}, {{ block.settings.accent_color | color_extract: 'green' }}, {{ block.settings.accent_color | color_extract: 'blue' }}, 0.05);
  }

  .luxury-mega-badges-{{ ai_gen_id }} {
    display: flex;
    gap: 8px;
    margin-bottom: 25px;
    flex-wrap: wrap;
  }

  .luxury-filter-badge-{{ ai_gen_id }} {
    background: linear-gradient(135deg, {{ block.settings.accent_color }}, {{ block.settings.accent_color | color_darken: 10 }});
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 8px rgba({{ block.settings.accent_color | color_extract: 'red' }}, {{ block.settings.accent_color | color_extract: 'green' }}, {{ block.settings.accent_color | color_extract: 'blue' }}, 0.3);
  }

  .luxury-filter-badge-{{ ai_gen_id }}:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba({{ block.settings.accent_color | color_extract: 'red' }}, {{ block.settings.accent_color | color_extract: 'green' }}, {{ block.settings.accent_color | color_extract: 'blue' }}, 0.4);
  }

  .luxury-trust-signal-{{ ai_gen_id }} {
    background: rgba({{ block.settings.accent_color | color_extract: 'red' }}, {{ block.settings.accent_color | color_extract: 'green' }}, {{ block.settings.accent_color | color_extract: 'blue' }}, 0.1);
    padding: 15px;
    border-radius: 12px;
    margin-top: 20px;
    border-left: 4px solid {{ block.settings.accent_color }};
  }

  .luxury-trust-text-{{ ai_gen_id }} {
    font-size: 12px;
    color: {{ block.settings.primary_color }};
    font-weight: 500;
    margin: 0;
  }

  .luxury-utilities-{{ ai_gen_id }} {
    justify-self: end;
    grid-column: 3;
    display: flex;
    align-items: center;
    gap: {{ block.settings.utility_spacing }}px;
  }

  .luxury-utility-btn-{{ ai_gen_id }} {
    background: none;
    border: none;
    cursor: pointer;
    padding: {{ block.settings.utility_padding }}px;
    color: {{ block.settings.primary_color }};
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    overflow: hidden;
    text-decoration: none;
  }

  .luxury-utility-btn-{{ ai_gen_id }}::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: linear-gradient(135deg, rgba({{ block.settings.accent_color | color_extract: 'red' }}, {{ block.settings.accent_color | color_extract: 'green' }}, {{ block.settings.accent_color | color_extract: 'blue' }}, 0.1), rgba({{ block.settings.accent_color | color_extract: 'red' }}, {{ block.settings.accent_color | color_extract: 'green' }}, {{ block.settings.accent_color | color_extract: 'blue' }}, 0.2));
    border-radius: 50%;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translate(-50%, -50%);
  }

  .luxury-utility-btn-{{ ai_gen_id }}:hover {
    color: {{ block.settings.accent_color }};
    transform: translateY(-2px);
  }

  .luxury-utility-btn-{{ ai_gen_id }}:hover::before {
    width: 100%;
    height: 100%;
  }

  .luxury-utility-btn-{{ ai_gen_id }} svg {
    width: {{ block.settings.icon_size }}px;
    height: {{ block.settings.icon_size }}px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    z-index: 1;
  }

  .luxury-utility-btn-{{ ai_gen_id }}:hover svg {
    transform: scale(1.15) rotate(5deg);
  }

  /* Currency/Language Selector */
  .luxury-currency-selector-{{ ai_gen_id }} {
    position: relative;
  }

  .luxury-currency-dropdown-{{ ai_gen_id }} {
    position: absolute;
    top: 100%;
    right: 0;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    min-width: 180px;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15), 0 4px 16px rgba(0, 0, 0, 0.08);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 200;
    border: 1px solid rgba({{ block.settings.accent_color | color_extract: 'red' }}, {{ block.settings.accent_color | color_extract: 'green' }}, {{ block.settings.accent_color | color_extract: 'blue' }}, 0.1);
    margin-top: 8px;
  }

  .luxury-currency-selector-{{ ai_gen_id }}:hover .luxury-currency-dropdown-{{ ai_gen_id }} {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }

  .luxury-currency-option-{{ ai_gen_id }} {
    padding: 12px 16px;
    color: {{ block.settings.primary_color }};
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba({{ block.settings.primary_color | color_extract: 'red' }}, {{ block.settings.primary_color | color_extract: 'green' }}, {{ block.settings.primary_color | color_extract: 'blue' }}, 0.08);
  }

  .luxury-currency-option-{{ ai_gen_id }}:hover {
    background: rgba({{ block.settings.accent_color | color_extract: 'red' }}, {{ block.settings.accent_color | color_extract: 'green' }}, {{ block.settings.accent_color | color_extract: 'blue' }}, 0.08);
    color: {{ block.settings.accent_color }};
  }

  .luxury-currency-option-{{ ai_gen_id }}:last-child {
    border-bottom: none;
  }

  .luxury-cart-counter-{{ ai_gen_id }} {
    position: absolute;
    top: {{ block.settings.utility_padding | times: 0.5 }}px;
    right: {{ block.settings.utility_padding | times: 0.5 }}px;
    background: linear-gradient(135deg, {{ block.settings.accent_color }}, {{ block.settings.accent_color | color_darken: 10 }});
    color: white;
    border-radius: 50%;
    width: {{ block.settings.counter_size }}px;
    height: {{ block.settings.counter_size }}px;
    font-size: {{ block.settings.counter_font_size }}px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    animation: luxury-cart-pulse-{{ ai_gen_id }} 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 2;
  }

  @keyframes luxury-cart-pulse-{{ ai_gen_id }} {
    0% {
      transform: scale(0.8);
      opacity: 0;
    }
    50% { 
      transform: scale(1.2);
      opacity: 1;
    }
    100% { 
      transform: scale(1);
      opacity: 1;
    }
  }

  .luxury-mobile-toggle-{{ ai_gen_id }} {
    display: none;
    background: none;
    border: none;
    cursor: pointer;
    padding: {{ block.settings.utility_padding }}px;
    color: {{ block.settings.primary_color }};
    min-width: 44px;
    min-height: 44px;
    transition: all 0.3s ease;
  }

  .luxury-mobile-toggle-{{ ai_gen_id }}:hover {
    color: {{ block.settings.accent_color }};
    transform: translateY(-2px);
  }

  .luxury-mobile-toggle-{{ ai_gen_id }} svg {
    width: {{ block.settings.icon_size }}px;
    height: {{ block.settings.icon_size }}px;
    transition: transform 0.3s ease;
  }

  .luxury-mobile-menu-{{ ai_gen_id }} {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, {{ block.settings.header_background }}, {{ block.settings.header_background | color_lighten: 5 }});
    z-index: 2000;
    transform: translateX(-100%);
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow-y: auto;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
  }

  .luxury-mobile-menu-{{ ai_gen_id }}.active {
    transform: translateX(0);
  }

  .luxury-mobile-header-{{ ai_gen_id }} {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px {{ block.settings.container_padding }}px;
    border-bottom: 1px solid rgba({{ block.settings.primary_color | color_extract: 'red' }}, {{ block.settings.primary_color | color_extract: 'green' }}, {{ block.settings.primary_color | color_extract: 'blue' }}, 0.1);
  }

  .luxury-mobile-close-{{ ai_gen_id }} {
    background: none;
    border: none;
    cursor: pointer;
    padding: 12px;
    color: {{ block.settings.primary_color }};
    min-width: 44px;
    min-height: 44px;
    transition: all 0.3s ease;
  }

  .luxury-mobile-close-{{ ai_gen_id }}:hover {
    color: {{ block.settings.accent_color }};
    transform: rotate(90deg);
  }

  .luxury-mobile-nav-{{ ai_gen_id }} {
    padding: 40px {{ block.settings.container_padding }}px;
  }

  .luxury-mobile-nav-{{ ai_gen_id }} a {
    display: flex;
    align-items: center;
    padding: 20px 0;
    color: {{ block.settings.primary_color }};
    text-decoration: none;
    font-size: {{ block.settings.nav_font_size | times: 1.2 }}px;
    font-weight: {{ block.settings.nav_font_weight }};
    text-transform: uppercase;
    letter-spacing: {{ block.settings.nav_letter_spacing }}px;
    border-bottom: 1px solid rgba({{ block.settings.primary_color | color_extract: 'red' }}, {{ block.settings.primary_color | color_extract: 'green' }}, {{ block.settings.primary_color | color_extract: 'blue' }}, 0.1);
    min-height: 44px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }

  .luxury-mobile-nav-{{ ai_gen_id }} a::before {
    content: '';
    position: absolute;
    left: -100%;
    top: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba({{ block.settings.accent_color | color_extract: 'red' }}, {{ block.settings.accent_color | color_extract: 'green' }}, {{ block.settings.accent_color | color_extract: 'blue' }}, 0.1), transparent);
    transition: left 0.5s ease;
  }

  .luxury-mobile-nav-{{ ai_gen_id }} a:hover {
    color: {{ block.settings.accent_color }};
    padding-left: 20px;
  }

  .luxury-mobile-nav-{{ ai_gen_id }} a:hover::before {
    left: 100%;
  }

  @media screen and (max-width: 1024px) {
    .luxury-nav-{{ ai_gen_id }} {
      display: none;
    }

    .luxury-mobile-toggle-{{ ai_gen_id }} {
      display: flex;
    }

    .luxury-header-container-{{ ai_gen_id }} {
      grid-template-columns: auto 1fr auto;
      padding: 0 20px;
    }

    .luxury-logo-{{ ai_gen_id }} {
      justify-self: center;
    }

    .luxury-utilities-{{ ai_gen_id }} {
      gap: 10px;
    }
  }

  @media screen and (max-width: 768px) {
    .luxury-header-container-{{ ai_gen_id }} {
      padding: 0 15px;
    }

    .luxury-logo-{{ ai_gen_id }} a {
      font-size: {{ block.settings.logo_size | times: 0.8 }}px;
    }

    .luxury-utilities-{{ ai_gen_id }} {
      gap: 5px;
    }

    .luxury-utility-btn-{{ ai_gen_id }} {
      padding: 8px;
    }

    .luxury-header-{{ ai_gen_id }} {
      height: {{ block.settings.header_height | times: 0.9 }}px;
    }

    .luxury-header-{{ ai_gen_id }}.scrolled {
      height: {{ block.settings.header_height | times: 0.7 }}px;
    }
  }

  body {
    padding-top: {{ block.settings.header_height }}px;
  }

  @media screen and (max-width: 768px) {
    body {
      padding-top: {{ block.settings.header_height | times: 0.9 }}px;
    }
  }
{% endstyle %}

<luxury-header-{{ ai_gen_id }} class="luxury-header-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  <div class="luxury-header-container-{{ ai_gen_id }}">
    <nav class="luxury-nav-{{ ai_gen_id }}">
      <ul class="luxury-nav-list-{{ ai_gen_id }}">
        <!-- Home -->
        <li class="luxury-nav-item-{{ ai_gen_id }}">
          <a href="/" class="luxury-nav-link-{{ ai_gen_id }}">Home</a>
        </li>

        <!-- Personal Care -->
        <li class="luxury-nav-item-{{ ai_gen_id }}">
          <a href="/collections/personal-care" class="luxury-nav-link-{{ ai_gen_id }}">Personal Care</a>
          <div class="luxury-mega-menu-{{ ai_gen_id }}">
            <div class="luxury-mega-badges-{{ ai_gen_id }}">
              <button class="luxury-filter-badge-{{ ai_gen_id }}">New</button>
              <button class="luxury-filter-badge-{{ ai_gen_id }}">Best Seller</button>
              <button class="luxury-filter-badge-{{ ai_gen_id }}">Dermatologist Approved</button>
              <button class="luxury-filter-badge-{{ ai_gen_id }}">Trending</button>
            </div>
            <div class="luxury-mega-grid-{{ ai_gen_id }}">
              <div class="luxury-mega-column-{{ ai_gen_id }}">
                <h3 class="luxury-mega-title-{{ ai_gen_id }}">Skincare</h3>
                <a href="/collections/ice-rollers" class="luxury-mega-link-{{ ai_gen_id }}">Ice Rollers</a>
                <a href="/collections/whitening-toothpaste" class="luxury-mega-link-{{ ai_gen_id }}">Whitening Toothpaste</a>
                <a href="/collections/face-serums" class="luxury-mega-link-{{ ai_gen_id }}">Face Serums</a>
                <a href="/collections/moisturizers" class="luxury-mega-link-{{ ai_gen_id }}">Moisturizers</a>
                <div class="luxury-trust-signal-{{ ai_gen_id }}">
                  <p class="luxury-trust-text-{{ ai_gen_id }}">✨ 4,000+ customers love our skincare range</p>
                </div>
              </div>
              <div class="luxury-mega-column-{{ ai_gen_id }}">
                <h3 class="luxury-mega-title-{{ ai_gen_id }}">Hair Care</h3>
                <a href="/collections/heatless-curlers" class="luxury-mega-link-{{ ai_gen_id }}">Heatless Curlers</a>
                <a href="/collections/herbal-hair-oil" class="luxury-mega-link-{{ ai_gen_id }}">Herbal Hair Oil</a>
                <a href="/collections/hair-styling-tools" class="luxury-mega-link-{{ ai_gen_id }}">Styling Tools</a>
                <a href="/collections/electric-shaver" class="luxury-mega-link-{{ ai_gen_id }}">Electric Shaver</a>
                <a href="/collections/scalp-applicator" class="luxury-mega-link-{{ ai_gen_id }}">Scalp Applicator</a>
              </div>
              <div class="luxury-mega-column-{{ ai_gen_id }}">
                <h3 class="luxury-mega-title-{{ ai_gen_id }}">Body & Bath</h3>
                <a href="/collections/body-brushes" class="luxury-mega-link-{{ ai_gen_id }}">Body Brushes</a>
                <a href="/collections/intimate-wash" class="luxury-mega-link-{{ ai_gen_id }}">Intimate Care</a>
                <a href="/collections/deodorants" class="luxury-mega-link-{{ ai_gen_id }}">Deodorants</a>
                <div class="luxury-trust-signal-{{ ai_gen_id }}">
                  <p class="luxury-trust-text-{{ ai_gen_id }}">🔥 Limited Edition Monsoon Collection</p>
                </div>
              </div>
              <div class="luxury-mega-column-{{ ai_gen_id }}">
                <h3 class="luxury-mega-title-{{ ai_gen_id }}">Grooming & Wellness</h3>
                <a href="/collections/mini-shaver" class="luxury-mega-link-{{ ai_gen_id }}">Mini Shaver</a>
                <a href="/collections/salon-sharpener" class="luxury-mega-link-{{ ai_gen_id }}">Salon Sharpener</a>
                <a href="/collections/aromatherapy" class="luxury-mega-link-{{ ai_gen_id }}">Aromatherapy</a>
                <a href="/collections/wellness-kits" class="luxury-mega-link-{{ ai_gen_id }}">Wellness Kits</a>
              </div>
            </div>
          </div>
        </li>

        <!-- Home Decor -->
        <li class="luxury-nav-item-{{ ai_gen_id }}">
          <a href="/collections/home-decor" class="luxury-nav-link-{{ ai_gen_id }}">Home Decor</a>
          <div class="luxury-mega-menu-{{ ai_gen_id }}">
            <div class="luxury-mega-badges-{{ ai_gen_id }}">
              <button class="luxury-filter-badge-{{ ai_gen_id }}">Living Room</button>
              <button class="luxury-filter-badge-{{ ai_gen_id }}">Bedroom</button>
              <button class="luxury-filter-badge-{{ ai_gen_id }}">Bathroom</button>
              <button class="luxury-filter-badge-{{ ai_gen_id }}">Festive</button>
            </div>
            <div class="luxury-mega-grid-{{ ai_gen_id }}">
              <div class="luxury-mega-column-{{ ai_gen_id }}">
                <h3 class="luxury-mega-title-{{ ai_gen_id }}">Lighting & Ambience</h3>
                <a href="/collections/moon-led-lights" class="luxury-mega-link-{{ ai_gen_id }}">Moon LED Lights</a>
                <a href="/collections/cabinet-lights" class="luxury-mega-link-{{ ai_gen_id }}">Cabinet Lights</a>
                <a href="/collections/aroma-diffusers" class="luxury-mega-link-{{ ai_gen_id }}">Aroma Diffusers</a>
              </div>
              <div class="luxury-mega-column-{{ ai_gen_id }}">
                <h3 class="luxury-mega-title-{{ ai_gen_id }}">Organizers & Accents</h3>
                <a href="/collections/rotating-hooks" class="luxury-mega-link-{{ ai_gen_id }}">Rotating Hooks</a>
                <a href="/collections/golden-hooks" class="luxury-mega-link-{{ ai_gen_id }}">Golden Large Hooks</a>
                <a href="/collections/decorative-trays" class="luxury-mega-link-{{ ai_gen_id }}">Decorative Trays</a>
                <div class="luxury-trust-signal-{{ ai_gen_id }}">
                  <p class="luxury-trust-text-{{ ai_gen_id }}">🏆 Editor's Choice for Luxury Organization</p>
                </div>
              </div>
              <div class="luxury-mega-column-{{ ai_gen_id }}">
                <h3 class="luxury-mega-title-{{ ai_gen_id }}">Tableware & Linens</h3>
                <a href="/collections/ribbed-glasses" class="luxury-mega-link-{{ ai_gen_id }}">Ribbed Glasses</a>
                <a href="/collections/premium-mats" class="luxury-mega-link-{{ ai_gen_id }}">Premium Mats</a>
                <a href="/collections/tableware-sets" class="luxury-mega-link-{{ ai_gen_id }}">Luxe Tableware</a>
              </div>
            </div>
          </div>
        </li>

        <!-- Daily Accessories -->
        <li class="luxury-nav-item-{{ ai_gen_id }}">
          <a href="/collections/daily-accessories" class="luxury-nav-link-{{ ai_gen_id }}">Accessories</a>
          <div class="luxury-mega-menu-{{ ai_gen_id }}">
            <div class="luxury-mega-badges-{{ ai_gen_id }}">
              <button class="luxury-filter-badge-{{ ai_gen_id }}">Utility</button>
              <button class="luxury-filter-badge-{{ ai_gen_id }}">Travel</button>
              <button class="luxury-filter-badge-{{ ai_gen_id }}">Fashion</button>
              <button class="luxury-filter-badge-{{ ai_gen_id }}">Eco-Friendly</button>
            </div>
            <div class="luxury-mega-grid-{{ ai_gen_id }}">
              <div class="luxury-mega-column-{{ ai_gen_id }}">
                <h3 class="luxury-mega-title-{{ ai_gen_id }}">Utility Tools</h3>
                <a href="/collections/manual-choppers" class="luxury-mega-link-{{ ai_gen_id }}">Manual Choppers</a>
                <a href="/collections/mops-scrubbers" class="luxury-mega-link-{{ ai_gen_id }}">Mops & Scrubbers</a>
                <a href="/collections/soap-dispensers" class="luxury-mega-link-{{ ai_gen_id }}">Soap Dispensers</a>
                <a href="/collections/graters" class="luxury-mega-link-{{ ai_gen_id }}">Graters</a>
              </div>
              <div class="luxury-mega-column-{{ ai_gen_id }}">
                <h3 class="luxury-mega-title-{{ ai_gen_id }}">Travel & Lifestyle</h3>
                <a href="/collections/mens-sling-bags" class="luxury-mega-link-{{ ai_gen_id }}">Men's Sling Bags</a>
                <a href="/collections/tech-organizers" class="luxury-mega-link-{{ ai_gen_id }}">Tech Organizers</a>
                <a href="/collections/jewelry-cleaners" class="luxury-mega-link-{{ ai_gen_id }}">Jewelry Cleaners</a>
              </div>
              <div class="luxury-mega-column-{{ ai_gen_id }}">
                <h3 class="luxury-mega-title-{{ ai_gen_id }}">Fashion Accessories</h3>
                <a href="/collections/stone-bracelets" class="luxury-mega-link-{{ ai_gen_id }}">Stone Bracelets</a>
                <a href="/collections/water-bottles" class="luxury-mega-link-{{ ai_gen_id }}">Water Bottles & Mugs</a>
                <div class="luxury-trust-signal-{{ ai_gen_id }}">
                  <p class="luxury-trust-text-{{ ai_gen_id }}">🌱 Sustainable & Ethically Sourced</p>
                </div>
              </div>
            </div>
          </div>
        </li>

        <!-- DIY & Tools -->
        <li class="luxury-nav-item-{{ ai_gen_id }}">
          <a href="/collections/diy-tools" class="luxury-nav-link-{{ ai_gen_id }}">DIY & Tools</a>
          <div class="luxury-mega-menu-{{ ai_gen_id }}">
            <div class="luxury-mega-badges-{{ ai_gen_id }}">
              <button class="luxury-filter-badge-{{ ai_gen_id }}">Kits</button>
              <button class="luxury-filter-badge-{{ ai_gen_id }}">Creative</button>
              <button class="luxury-filter-badge-{{ ai_gen_id }}">Seasonal</button>
            </div>
            <div class="luxury-mega-grid-{{ ai_gen_id }}">
              <div class="luxury-mega-column-{{ ai_gen_id }}">
                <h3 class="luxury-mega-title-{{ ai_gen_id }}">Kits & Supplies</h3>
                <a href="/collections/art-craft-kits" class="luxury-mega-link-{{ ai_gen_id }}">Art & Craft Kits</a>
                <a href="/collections/fruit-vegetable-choppers" class="luxury-mega-link-{{ ai_gen_id }}">Kitchen Choppers</a>
                <a href="/collections/gujiya-maker" class="luxury-mega-link-{{ ai_gen_id }}">Gujiya/Momos Maker</a>
              </div>
              <div class="luxury-mega-column-{{ ai_gen_id }}">
                <h3 class="luxury-mega-title-{{ ai_gen_id }}">Creative/Seasonal</h3>
                <a href="/collections/plant-care" class="luxury-mega-link-{{ ai_gen_id }}">Plant Care</a>
                <a href="/collections/workshop-kits" class="luxury-mega-link-{{ ai_gen_id }}">Workshop Kits</a>
                <div class="luxury-trust-signal-{{ ai_gen_id }}">
                  <p class="luxury-trust-text-{{ ai_gen_id }}">🎨 Perfect for Creative Minds</p>
                </div>
              </div>
            </div>
          </div>
        </li>

        <!-- Gifting -->
        <li class="luxury-nav-item-{{ ai_gen_id }}">
          <a href="/collections/gifting" class="luxury-nav-link-{{ ai_gen_id }}">Gifting</a>
          <div class="luxury-mega-menu-{{ ai_gen_id }}">
            <div class="luxury-mega-badges-{{ ai_gen_id }}">
              <button class="luxury-filter-badge-{{ ai_gen_id }}">Under ₹500</button>
              <button class="luxury-filter-badge-{{ ai_gen_id }}">Under ₹1000</button>
              <button class="luxury-filter-badge-{{ ai_gen_id }}">Premium</button>
            </div>
            <div class="luxury-mega-grid-{{ ai_gen_id }}">
              <div class="luxury-mega-column-{{ ai_gen_id }}">
                <h3 class="luxury-mega-title-{{ ai_gen_id }}">Gift Sets</h3>
                <a href="/collections/gift-sets" class="luxury-mega-link-{{ ai_gen_id }}">Curated Gift Sets</a>
                <a href="/gift-cards" class="luxury-mega-link-{{ ai_gen_id }}">Gift Cards</a>
                <a href="/collections/occasion-picks" class="luxury-mega-link-{{ ai_gen_id }}">Occasion Picks</a>
                <div class="luxury-trust-signal-{{ ai_gen_id }}">
                  <p class="luxury-trust-text-{{ ai_gen_id }}">🎁 Free Premium Gift Wrapping Available</p>
                </div>
              </div>
            </div>
          </div>
        </li>

        <!-- Sale/Discover -->
        <li class="luxury-nav-item-{{ ai_gen_id }}">
          <a href="/collections/sale" class="luxury-nav-link-{{ ai_gen_id }}">Discover</a>
          <div class="luxury-mega-menu-{{ ai_gen_id }}">
            <div class="luxury-mega-badges-{{ ai_gen_id }}">
              <button class="luxury-filter-badge-{{ ai_gen_id }}">Sale</button>
              <button class="luxury-filter-badge-{{ ai_gen_id }}">New Arrivals</button>
              <button class="luxury-filter-badge-{{ ai_gen_id }}">Editor's Pick</button>
            </div>
            <div class="luxury-mega-grid-{{ ai_gen_id }}">
              <div class="luxury-mega-column-{{ ai_gen_id }}">
                <h3 class="luxury-mega-title-{{ ai_gen_id }}">New Arrivals</h3>
                <a href="/collections/new-arrivals" class="luxury-mega-link-{{ ai_gen_id }}">Latest Products</a>
                <a href="/collections/seasonal" class="luxury-mega-link-{{ ai_gen_id }}">Seasonal Collection</a>
                <a href="/collections/editors-picks" class="luxury-mega-link-{{ ai_gen_id }}">Editor's Picks</a>
                <div class="luxury-trust-signal-{{ ai_gen_id }}">
                  <p class="luxury-trust-text-{{ ai_gen_id }}">🔥 Limited Time: Up to 50% Off</p>
                </div>
              </div>
            </div>
          </div>
        </li>
      </ul>
    </nav>

    <div class="luxury-logo-{{ ai_gen_id }}">
      {% if block.settings.logo_image %}
        <a href="/">
          <img
            src="{{ block.settings.logo_image | image_url: width: 400}}"
            alt="{{ shop.name }}"
            width="400"
            height="133"
            loading="eager"
            class="luxury-logo-image-{{ ai_gen_id }}"
          >
        </a>
      {% else %}
        <a href="/">{{ block.settings.logo_text | default: "LIFESTYLE" }}</a>
      {% endif %}
    </div>

    <div class="luxury-utilities-{{ ai_gen_id }}">
      <button class="luxury-utility-btn-{{ ai_gen_id }}" data-search-toggle aria-label="Search">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <circle cx="11" cy="11" r="8"></circle>
          <path d="m21 21-4.35-4.35"></path>
        </svg>
      </button>

      <a href="/account" class="luxury-utility-btn-{{ ai_gen_id }}" aria-label="Account">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
          <circle cx="12" cy="7" r="4"></circle>
        </svg>
      </a>

      {% if block.settings.show_wishlist %}
        <a href="/pages/wishlist" class="luxury-utility-btn-{{ ai_gen_id }}" aria-label="Wishlist">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
          </svg>
        </a>
      {% endif %}

      <a href="/cart" class="luxury-utility-btn-{{ ai_gen_id }}" aria-label="Cart">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"></path>
          <line x1="3" y1="6" x2="21" y2="6"></line>
          <path d="M16 10a4 4 0 0 1-8 0"></path>
        </svg>
        {% if cart.item_count > 0 %}
          <span class="luxury-cart-counter-{{ ai_gen_id }}">{{ cart.item_count }}</span>
        {% endif %}
      </a>

      <div class="luxury-currency-selector-{{ ai_gen_id }}">
        <button class="luxury-utility-btn-{{ ai_gen_id }}" aria-label="Currency & Language">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="2" y1="12" x2="22" y2="12"></line>
            <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
          </svg>
        </button>
        <div class="luxury-currency-dropdown-{{ ai_gen_id }}">
          <a href="#" class="luxury-currency-option-{{ ai_gen_id }}">
            <span>🇮🇳</span>
            <span>INR ₹</span>
          </a>
          <a href="#" class="luxury-currency-option-{{ ai_gen_id }}">
            <span>🇺🇸</span>
            <span>USD $</span>
          </a>
          <a href="#" class="luxury-currency-option-{{ ai_gen_id }}">
            <span>🇬🇧</span>
            <span>GBP £</span>
          </a>
          <a href="#" class="luxury-currency-option-{{ ai_gen_id }}">
            <span>🇪🇺</span>
            <span>EUR €</span>
          </a>
        </div>
      </div>

      <button class="luxury-mobile-toggle-{{ ai_gen_id }}" data-mobile-toggle aria-label="Menu">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <line x1="3" y1="6" x2="21" y2="6"></line>
          <line x1="3" y1="12" x2="21" y2="12"></line>
          <line x1="3" y1="18" x2="21" y2="18"></line>
        </svg>
      </button>
    </div>
  </div>

  <div class="luxury-mobile-menu-{{ ai_gen_id }}" data-mobile-menu>
    <div class="luxury-mobile-header-{{ ai_gen_id }}">
      <div class="luxury-logo-{{ ai_gen_id }}">
        {% if block.settings.logo_image %}
          <a href="/">
            <img
              src="{{ block.settings.logo_image | image_url: width: 200 }}"
              alt="{{ shop.name }}"
              width="200"
              height="67"
              loading="lazy"
            >
          </a>
        {% else %}
          <a href="/">{{ block.settings.logo_text | default: "LIFESTYLE" }}</a>
        {% endif %}
      </div>
      <button class="luxury-mobile-close-{{ ai_gen_id }}" data-mobile-close aria-label="Close menu">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <line x1="18" y1="6" x2="6" y2="18"></line>
          <line x1="6" y1="6" x2="18" y2="18"></line>
        </svg>
      </button>
    </div>
    <nav class="luxury-mobile-nav-{{ ai_gen_id }}">
      <a href="/">Home</a>
      <a href="/collections/personal-care">Personal Care</a>
      <a href="/collections/home-decor">Home Decor</a>
      <a href="/collections/daily-accessories">Daily Accessories</a>
      <a href="/collections/diy-tools">DIY & Tools</a>
      <a href="/collections/gifting">Gifting</a>
      <a href="/collections/sale">Discover</a>
      
      <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid rgba({{ block.settings.primary_color | color_extract: 'red' }}, {{ block.settings.primary_color | color_extract: 'green' }}, {{ block.settings.primary_color | color_extract: 'blue' }}, 0.1);">
        <a href="/pages/about">Brand Story</a>
        <a href="/pages/sustainability">Sustainability</a>
        <a href="/pages/contact">Contact</a>
      </div>
    </nav>
  </div>
</luxury-header-{{ ai_gen_id }}>

<script>
(function() {
  class LuxuryHeader{{ ai_gen_id }} extends HTMLElement {
    constructor() {
      super();
      this.scrollThreshold = 50;
      this.isScrolled = false;
    }

    connectedCallback() {
      this.header = this;
      this.mobileMenu = this.querySelector('[data-mobile-menu]');
      this.setupScrollEffect();
      this.setupMobileMenu();
      this.setupCartCounter();
      this.setupSearch();
      this.setupMegaMenus();
      this.setupCurrencySelector();
      this.setupFilterBadges();
    }

    setupScrollEffect() {
      let ticking = false;
      
      const updateHeader = () => {
        const scrollY = window.scrollY;
        const shouldBeScrolled = scrollY > this.scrollThreshold;
        
        if (shouldBeScrolled !== this.isScrolled) {
          this.isScrolled = shouldBeScrolled;
          this.header.classList.toggle('scrolled', this.isScrolled);
        }
        ticking = false;
      };

      window.addEventListener('scroll', () => {
        if (!ticking) {
          requestAnimationFrame(updateHeader);
          ticking = true;
        }
      }, { passive: true });
    }

    setupSearch() {
      const searchToggle = this.querySelector('[data-search-toggle]');
      
      searchToggle.addEventListener('click', () => {
        window.location.href = '/search';
      });
    }

    setupMegaMenus() {
      const navItems = this.querySelectorAll('.luxury-nav-item-{{ ai_gen_id }}');
      
      navItems.forEach(item => {
        const megaMenu = item.querySelector('.luxury-mega-menu-{{ ai_gen_id }}');
        if (!megaMenu) return;

        let timeout;
        
        item.addEventListener('mouseenter', () => {
          clearTimeout(timeout);
          megaMenu.style.opacity = '1';
          megaMenu.style.visibility = 'visible';
          megaMenu.style.transform = 'translateY(0)';
        });

        item.addEventListener('mouseleave', () => {
          timeout = setTimeout(() => {
            megaMenu.style.opacity = '0';
            megaMenu.style.visibility = 'hidden';
            megaMenu.style.transform = 'translateY(-20px)';
          }, 150);
        });
      });
    }

    setupCurrencySelector() {
      const currencySelector = this.querySelector('.luxury-currency-selector-{{ ai_gen_id }}');
      const dropdown = currencySelector?.querySelector('.luxury-currency-dropdown-{{ ai_gen_id }}');
      
      if (!currencySelector || !dropdown) return;

      let timeout;

      currencySelector.addEventListener('mouseenter', () => {
        clearTimeout(timeout);
        dropdown.style.opacity = '1';
        dropdown.style.visibility = 'visible';
        dropdown.style.transform = 'translateY(0)';
      });

      currencySelector.addEventListener('mouseleave', () => {
        timeout = setTimeout(() => {
          dropdown.style.opacity = '0';
          dropdown.style.visibility = 'hidden';
          dropdown.style.transform = 'translateY(-10px)';
        }, 150);
      });

      const options = dropdown.querySelectorAll('.luxury-currency-option-{{ ai_gen_id }}');
      options.forEach(option => {
        option.addEventListener('click', (e) => {
          e.preventDefault();
          // Handle currency change logic here
          console.log('Currency changed to:', option.textContent.trim());
        });
      });
    }

    setupFilterBadges() {
      const badges = this.querySelectorAll('.luxury-filter-badge-{{ ai_gen_id }}');
      
      badges.forEach(badge => {
        badge.addEventListener('click', () => {
          // Handle filter logic here
          const filterType = badge.textContent.trim();
          console.log('Filter applied:', filterType);
          
          // Add visual feedback
          badge.style.transform = 'scale(0.95)';
          setTimeout(() => {
            badge.style.transform = 'scale(1)';
          }, 150);
        });
      });
    }

    setupMobileMenu() {
      const mobileToggle = this.querySelector('[data-mobile-toggle]');
      const mobileClose = this.querySelector('[data-mobile-close]');

      mobileToggle.addEventListener('click', () => {
        this.openMobileMenu();
      });

      mobileClose.addEventListener('click', () => {
        this.closeMobileMenu();
      });

      this.mobileMenu.addEventListener('click', (e) => {
        if (e.target === this.mobileMenu) {
          this.closeMobileMenu();
        }
      });
      
      document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && this.mobileMenu.classList.contains('active')) {
          this.closeMobileMenu();
        }
      });
    }

    openMobileMenu() {
      this.mobileMenu.classList.add('active');
      document.body.style.overflow = 'hidden';
      document.body.style.position = 'fixed';
      document.body.style.width = '100%';
    }

    closeMobileMenu() {
      this.mobileMenu.classList.remove('active');
      document.body.style.overflow = '';
      document.body.style.position = '';
      document.body.style.width = '';
    }

    setupCartCounter() {
      document.addEventListener('cart:updated', () => {
        this.updateCartCounter();
      });

      fetch('/cart.js')
        .then(response => response.json())
        .then(cart => {
          this.updateCartDisplay(cart);
        })
        .catch(() => {});
    }

    updateCartCounter() {
      fetch('/cart.js')
        .then(response => response.json())
        .then(cart => {
          this.updateCartDisplay(cart);
        })
        .catch(() => {});
    }

    updateCartDisplay(cart) {
      const counter = this.querySelector('.luxury-cart-counter-{{ ai_gen_id }}');
      const cartBtn = this.querySelector('a[href="/cart"]');
      
      if (cart.item_count > 0) {
        if (counter) {
          counter.textContent = cart.item_count;
          counter.style.animation = 'none';
          counter.offsetHeight;
          counter.style.animation = 'luxury-cart-pulse-{{ ai_gen_id }} 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
        } else {
          const newCounter = document.createElement('span');
          newCounter.className = 'luxury-cart-counter-{{ ai_gen_id }}';
          newCounter.textContent = cart.item_count;
          cartBtn.appendChild(newCounter);
        }
      } else if (counter) {
        counter.remove();
      }
    }
  }

  customElements.define('luxury-header-{{ ai_gen_id }}', LuxuryHeader{{ ai_gen_id }});
})();
</script>

{% schema %}
{
  "name": "Premium Lifestyle Header",
  "tag": null,
  "settings": [
    {
      "type": "header",
      "content": "Logo Settings"
    },
    {
      "type": "image_picker",
      "id": "logo_image",
      "label": "Logo image"
    },
    {
      "type": "text",
      "id": "logo_text",
      "label": "Logo text",
      "default": "LIFESTYLE"
    },
    {
      "type": "font_picker",
      "id": "logo_font",
      "label": "Logo font",
      "default": "playfair_display_n4"
    },
    {
      "type": "range",
      "id": "logo_size",
      "min": 24,
      "max": 64,
      "step": 2,
      "unit": "px",
      "label": "Logo text size",
      "default": 40
    },
    {
      "type": "range",
      "id": "logo_height",
      "min": 40,
      "max": 120,
      "step": 5,
      "unit": "px",
      "label": "Logo image height",
      "default": 70
    },
    {
      "type": "range",
      "id": "logo_letter_spacing",
      "min": 0,
      "max": 6,
      "step": 0.5,
      "unit": "px",
      "label": "Logo letter spacing",
      "default": 2.5
    },
    {
      "type": "header",
      "content": "Header Design"
    },
    {
      "type": "range",
      "id": "header_height",
      "min": 70,
      "max": 140,
      "step": 5,
      "unit": "px",
      "label": "Header height",
      "default": 100
    },
    {
      "type": "range",
      "id": "container_width",
      "min": 1200,
      "max": 1800,
      "step": 50,
      "unit": "px",
      "label": "Container width",
      "default": 1600
    },
    {
      "type": "range",
      "id": "container_padding",
      "min": 20,
      "max": 60,
      "step": 5,
      "unit": "px",
      "label": "Container padding",
      "default": 40
    },
    {
      "type": "color",
      "id": "header_background",
      "label": "Background color",
      "default": "#FFFFFF"
    },
    {
      "type": "color",
      "id": "primary_color",
      "label": "Primary color (Deep Charcoal)",
      "default": "#2C2C2C"
    },
    {
      "type": "color",
      "id": "accent_color",
      "label": "Accent color (Warm Gold)",
      "default": "#D4AF37"
    },
    {
      "type": "header",
      "content": "Navigation Style"
    },
    {
      "type": "range",
      "id": "nav_font_size",
      "min": 12,
      "max": 18,
      "step": 1,
      "unit": "px",
      "label": "Navigation font size",
      "default": 14
    },
    {
      "type": "range",
      "id": "nav_font_weight",
      "min": 400,
      "max": 700,
      "step": 100,
      "label": "Navigation font weight",
      "default": 600
    },
    {
      "type": "range",
      "id": "nav_letter_spacing",
      "min": 0,
      "max": 4,
      "step": 0.5,
      "unit": "px",
      "label": "Navigation letter spacing",
      "default": 1.5
    },
    {
      "type": "range",
      "id": "nav_spacing",
      "min": 15,
      "max": 60,
      "step": 5,
      "unit": "px",
      "label": "Navigation spacing",
      "default": 35
    },
    {
      "type": "range",
      "id": "nav_padding",
      "min": 10,
      "max": 26,
      "step": 2,
      "unit": "px",
      "label": "Navigation padding",
      "default": 16
    },
    {
      "type": "header",
      "content": "Utility Icons"
    },
    {
      "type": "checkbox",
      "id": "show_wishlist",
      "label": "Show wishlist icon",
      "default": true
    },
    {
      "type": "range",
      "id": "icon_size",
      "min": 18,
      "max": 32,
      "step": 2,
      "unit": "px",
      "label": "Icon size",
      "default": 22
    },
    {
      "type": "range",
      "id": "utility_spacing",
      "min": 15,
      "max": 35,
      "step": 5,
      "unit": "px",
      "label": "Utility spacing",
      "default": 25
    },
    {
      "type": "range",
      "id": "utility_padding",
      "min": 10,
      "max": 20,
      "step": 2,
      "unit": "px",
      "label": "Utility padding",
      "default": 14
    },
    {
      "type": "range",
      "id": "counter_size",
      "min": 18,
      "max": 28,
      "step": 2,
      "unit": "px",
      "label": "Cart counter size",
      "default": 22
    },
    {
      "type": "range",
      "id": "counter_font_size",
      "min": 10,
      "max": 16,
      "step": 1,
      "unit": "px",
      "label": "Cart counter font size",
      "default": 12
    }
  ],
  "presets": [
    {
      "name": "Premium Lifestyle Header"
    }
  ]
}
{% endschema %}