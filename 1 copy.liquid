{% doc %}
  @prompt
    Create a luxury header and navigation component with:
    
    HEADER STRUCTURE:
    - Clean minimalist design (max 20% viewport height)
    - Centered logo with premium serif typography (Playfair Display style)
    - Horizontal navigation with elegant hover animations
    - Right-aligned utility icons: search, account, wishlist, cart with animated counter
    - Subtle shadow/gradient separating header from body
    - Sticky navigation that becomes translucent on scroll
    
    NAVIGATION FEATURES:
    - Multi-column dropdown mega-menus with high-quality category images
    - Quick filters for product discovery
    - Search with predictive results and auto-suggestions
    - Currency/language selectors
    
    COLOR SCHEME:
    - Primary: Deep charcoal (#2C2C2C) or rich navy (#1B365D)
    - Secondary: Warm gold (#D4AF37) accents
    - Pure white (#FFFFFF) background
    
    INTERACTIONS:
    - Smooth hover animations on menu items
    - Translucent effect on scroll
    - Mobile hamburger menu with full-screen overlay
    - Touch-friendly 44px minimum touch targets, HEADER STRUCTURE
    Employ a clean, minimalist design for the header, ensuring it never exceeds 20% of the viewport height for an unobtrusive look.
    
    Center the logo using premium serif typography inspired by Playfair Display, lending a classic, luxury feel.
    
    Integrate a horizontal navigation menu with category titles in crisp uppercase, spaced for elegance. Each menu item should feature subtle, smooth hover animations (color shift and underline).
    
    Align utility icons to the right:
    
    Search (magnifying glass)
    
    Account (user/avatar icon)
    
    Wishlist (heart icon)
    
    Cart (cart icon with animated item count)
    
    Add a subtle drop shadow or soft gradient line to visually separate header from the page body, ensuring light depth without clutter.
    
    Enable a sticky navigation effect: as users scroll down, the entire header becomes slightly translucent with a blur overlay for an ultra-modern feel, and reduces in height for minimal footprint.

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .luxury-header-{{ ai_gen_id }} {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background-color: {{ block.settings.header_background }};
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
    max-height: 20vh;
    height: {{ block.settings.header_height }}px;
    border-bottom: 1px solid rgba({{ block.settings.accent_color | color_extract: 'red' }}, {{ block.settings.accent_color | color_extract: 'green' }}, {{ block.settings.accent_color | color_extract: 'blue' }}, 0.1);
  }

  .luxury-header-{{ ai_gen_id }}.scrolled {
    background-color: rgba(255, 255, 255, 0.92);
    backdrop-filter: blur(20px);-webkit-backdrop-filter: blur(20px);
    height: {{ block.settings.header_height | times: 0.8 }}px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07), 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .luxury-header-container-{{ ai_gen_id }} {
    max-width: {{ block.settings.container_width }}px;
    margin: 0 auto;
    padding: 0 {{ block.settings.container_padding }}px;
    display: grid;
    grid-template-columns: 1fr auto1fr;
    align-items: center;
    height: 100%;
    position: relative;
  }

  .luxury-logo-{{ ai_gen_id }} {
    justify-self: center;
    grid-column: 2;
    transition: transform 0.3s ease;
  }

  .luxury-header-{{ ai_gen_id }}.scrolled .luxury-logo-{{ ai_gen_id }} {
    transform: scale(0.9);
  }

  .luxury-logo-{{ ai_gen_id }} a {
    text-decoration: none;
    color: {{ block.settings.primary_color }};
    font-family: {{ block.settings.logo_font.family }}, 'Playfair Display', serif;
    font-size: {{ block.settings.logo_size }}px;
    font-weight: {{ block.settings.logo_font.weight | default: 400 }};
    letter-spacing: {{ block.settings.logo_letter_spacing }}px;
    transition: all 0.3s ease;
    display: block;
  }

  .luxury-logo-{{ ai_gen_id }} a:hover {
    color: {{ block.settings.accent_color }};
    transform: translateY(-1px);
  }

  .luxury-logo-{{ ai_gen_id }} img {
    max-height: {{ block.settings.logo_height }}px;
    width: auto;
    transition: all 0.3s ease;
  }

  .luxury-header-{{ ai_gen_id }}.scrolled .luxury-logo-{{ ai_gen_id }} img {
    max-height: {{ block.settings.logo_height | times: 0.8 }}px;
  }

  .luxury-nav-{{ ai_gen_id }} {
    justify-self: start;
    grid-column: 1;
  }

  .luxury-nav-list-{{ ai_gen_id }} {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: {{ block.settings.nav_spacing }}px;
  }

  .luxury-nav-item-{{ ai_gen_id }} {
    position: relative;}

  .luxury-nav-link-{{ ai_gen_id }} {
    display: flex;
    align-items: center;
    padding: {{ block.settings.nav_padding }}px {{ block.settings.nav_padding | times: 1.5 }}px;
    text-decoration: none;
    color: {{ block.settings.primary_color }};
    font-size: {{ block.settings.nav_font_size }}px;
    font-weight: {{ block.settings.nav_font_weight }};
    letter-spacing: {{ block.settings.nav_letter_spacing }}px;
    text-transform: uppercase;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    min-height: 44px;
    overflow: hidden;
  }

  .luxury-nav-link-{{ ai_gen_id }}::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, {{ block.settings.accent_color }}, {{ block.settings.accent_color | color_lighten: 20 }});
    transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .luxury-nav-link-{{ ai_gen_id }}::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba({{ block.settings.accent_color | color_extract: 'red' }}, {{ block.settings.accent_color | color_extract: 'green' }}, {{ block.settings.accent_color | color_extract: 'blue' }}, 0.1), transparent);
    transition: left 0.5s ease;
  }

  .luxury-nav-link-{{ ai_gen_id }}:hover {
    color: {{ block.settings.accent_color }};
    transform: translateY(-2px);
  }

  .luxury-nav-link-{{ ai_gen_id }}:hover::before {
    width: 100%;
  }

  .luxury-nav-link-{{ ai_gen_id }}:hover::after {
    left: 100%;
  }

  .luxury-utilities-{{ ai_gen_id }} {
    justify-self: end;
    grid-column: 3;
    display: flex;
    align-items: center;
    gap: {{ block.settings.utility_spacing }}px;
  }

  .luxury-utility-btn-{{ ai_gen_id }} {
    background: none;
    border: none;
    cursor: pointer;
    padding: {{ block.settings.utility_padding }}px;
    color: {{ block.settings.primary_color }};
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    overflow: hidden;
  }

  .luxury-utility-btn-{{ ai_gen_id }}::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background-color: rgba({{ block.settings.accent_color | color_extract: 'red' }}, {{ block.settings.accent_color | color_extract: 'green' }}, {{ block.settings.accent_color | color_extract: 'blue' }}, 0.1);
    border-radius: 50%;
    transition: all 0.3s ease;
    transform: translate(-50%, -50%);
  }

  .luxury-utility-btn-{{ ai_gen_id }}:hover {
    color: {{ block.settings.accent_color }};
    transform: translateY(-3px);
  }

  .luxury-utility-btn-{{ ai_gen_id }}:hover::before {
    width: 100%;
    height: 100%;
  }

  .luxury-utility-btn-{{ ai_gen_id }} svg {
    width: {{ block.settings.icon_size }}px;
    height: {{ block.settings.icon_size }}px;
    transition: transform 0.3s ease;position: relative;
    z-index: 1;
  }

  .luxury-utility-btn-{{ ai_gen_id }}:hover svg {
    transform: scale(1.1);
  }

  .luxury-cart-counter-{{ ai_gen_id }} {
    position: absolute;
    top: {{ block.settings.utility_padding | times: 0.5 }}px;
    right: {{ block.settings.utility_padding | times: 0.5 }}px;
    background: linear-gradient(135deg, {{ block.settings.accent_color }}, {{ block.settings.accent_color | color_darken: 10 }});
    color: white;
    border-radius: 50%;
    width: {{ block.settings.counter_size }}px;
    height: {{ block.settings.counter_size }}px;
    font-size: {{ block.settings.counter_font_size }}px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    animation: luxury-cart-pulse-{{ ai_gen_id }} 0.6s cubic-bezier(0.4, 0, 0.2, 1);z-index: 2;
  }

  @keyframes luxury-cart-pulse-{{ ai_gen_id }} {
    0% {
      transform: scale(0.8);
      opacity: 0;
    }
    50% { 
      transform: scale(1.2);
      opacity: 1;
    }
    100% { 
      transform: scale(1);
      opacity: 1;
    }
  }

  .luxury-mobile-toggle-{{ ai_gen_id }} {
    display: none;
    background: none;
    border: none;
    cursor: pointer;
    padding: {{ block.settings.utility_padding }}px;
    color: {{ block.settings.primary_color }};
    min-width: 44px;
    min-height: 44px;
    transition: all 0.3s ease;
  }

  .luxury-mobile-toggle-{{ ai_gen_id }}:hover {
    color: {{ block.settings.accent_color }};transform: translateY(-2px);
  }

  .luxury-mobile-toggle-{{ ai_gen_id }} svg {
    width: {{ block.settings.icon_size }}px;
    height: {{ block.settings.icon_size }}px;
    transition: transform 0.3s ease;
  }

  .luxury-mobile-menu-{{ ai_gen_id }} {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, {{ block.settings.header_background }}, {{ block.settings.header_background | color_lighten: 5 }});
    z-index: 2000;
    transform: translateX(-100%);
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow-y: auto;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
  }

  .luxury-mobile-menu-{{ ai_gen_id }}.active {
    transform: translateX(0);
  }

  .luxury-mobile-header-{{ ai_gen_id }} {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px {{ block.settings.container_padding }}px;
    border-bottom: 1px solid rgba({{ block.settings.primary_color | color_extract: 'red' }}, {{ block.settings.primary_color | color_extract: 'green' }}, {{ block.settings.primary_color | color_extract: 'blue' }}, 0.1);}

  .luxury-mobile-close-{{ ai_gen_id }} {
    background: none;
    border: none;
    cursor: pointer;
    padding: 12px;
    color: {{ block.settings.primary_color }};
    min-width: 44px;
    min-height: 44px;
    transition: all 0.3s ease;
  }

  .luxury-mobile-close-{{ ai_gen_id }}:hover {
    color: {{ block.settings.accent_color }};
    transform: rotate(90deg);
  }

  .luxury-mobile-nav-{{ ai_gen_id }} {
    padding: 40px {{ block.settings.container_padding }}px;
  }

  .luxury-mobile-nav-{{ ai_gen_id }} a {
    display: flex;
    align-items: center;
    padding: 20px 0;
    color: {{ block.settings.primary_color }};
    text-decoration: none;
    font-size: {{ block.settings.nav_font_size | times: 1.2 }}px;
    font-weight: {{ block.settings.nav_font_weight }};
    text-transform: uppercase;
    letter-spacing: {{ block.settings.nav_letter_spacing }}px;
    border-bottom: 1px solid rgba({{ block.settings.primary_color | color_extract: 'red' }}, {{ block.settings.primary_color | color_extract: 'green' }}, {{ block.settings.primary_color | color_extract: 'blue' }}, 0.1);
    min-height: 44px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }

  .luxury-mobile-nav-{{ ai_gen_id }} a::before {
    content: '';
    position: absolute;
    left: -100%;
    top: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba({{ block.settings.accent_color | color_extract: 'red' }}, {{ block.settings.accent_color | color_extract: 'green' }}, {{ block.settings.accent_color | color_extract: 'blue' }}, 0.1), transparent);
    transition: left 0.5s ease;
  }

  .luxury-mobile-nav-{{ ai_gen_id }} a:hover {
    color: {{ block.settings.accent_color }};
    padding-left: 20px;
  }

  .luxury-mobile-nav-{{ ai_gen_id }} a:hover::before {
    left: 100%;
  }

  @media screen and (max-width: 1024px) {
    .luxury-nav-{{ ai_gen_id }} {
      display: none;
    }

    .luxury-mobile-toggle-{{ ai_gen_id }} {
      display: flex;
    }

    .luxury-header-container-{{ ai_gen_id }} {
      grid-template-columns: auto 1fr auto;
      padding: 0 20px;
    }

    .luxury-logo-{{ ai_gen_id }} {
      justify-self: center;
    }

    .luxury-utilities-{{ ai_gen_id }} {
      gap: 10px;
    }
  }

  @media screen and (max-width: 768px) {
    .luxury-header-container-{{ ai_gen_id }} {
      padding: 0 15px;
    }

    .luxury-logo-{{ ai_gen_id }} a {
      font-size: {{ block.settings.logo_size | times: 0.8 }}px;
    }

    .luxury-utilities-{{ ai_gen_id }} {
      gap: 5px;
    }

    .luxury-utility-btn-{{ ai_gen_id }} {
      padding: 8px;}

    .luxury-header-{{ ai_gen_id }} {
      height: {{ block.settings.header_height | times: 0.9 }}px;
    }

    .luxury-header-{{ ai_gen_id }}.scrolled {
      height: {{ block.settings.header_height | times: 0.7 }}px;
    }
  }

  body {
    padding-top: {{ block.settings.header_height }}px;
  }

  @media screen and (max-width: 768px) {
    body {
      padding-top: {{ block.settings.header_height | times: 0.9 }}px;
    }
  }
{% endstyle %}

<luxury-header-{{ ai_gen_id }} class="luxury-header-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  <div class="luxury-header-container-{{ ai_gen_id }}">
    <nav class="luxury-nav-{{ ai_gen_id }}">
      <ul class="luxury-nav-list-{{ ai_gen_id }}">
        {% for i in (1..5) %}
          {% assign menu_text_key = 'menu_' | append: i | append: '_text' %}
          {% assign menu_link_key = 'menu_' | append: i | append: '_link' %}
          {% assign menu_enabled_key = 'menu_' | append: i | append: '_enabled' %}

          {% if block.settings[menu_enabled_key] and block.settings[menu_text_key] != blank %}
            <li class="luxury-nav-item-{{ ai_gen_id }}">
              <a href="{{ block.settings[menu_link_key] }}" class="luxury-nav-link-{{ ai_gen_id }}">
                {{ block.settings[menu_text_key] }}
              </a>
            </li>
          {% endif %}
        {% endfor %}
      </ul>
    </nav>

    <div class="luxury-logo-{{ ai_gen_id }}">
      {% if block.settings.logo_image %}
        <a href="/">
          <img
            src="{{ block.settings.logo_image | image_url: width: 400}}"
            alt="{{ shop.name }}"
            width="400"
            height="133"
            loading="eager"
            class="luxury-logo-image-{{ ai_gen_id }}"
          >
        </a>
      {% else %}
        <a href="/">{{ block.settings.logo_text | default: shop.name }}</a>
      {% endif %}
    </div>

    <div class="luxury-utilities-{{ ai_gen_id }}">
      <button class="luxury-utility-btn-{{ ai_gen_id }}" data-search-toggle aria-label="Search">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <circle cx="11" cy="11" r="8"></circle>
          <path d="m21 21-4.35-4.35"></path>
        </svg>
      </button>

      <a href="/account" class="luxury-utility-btn-{{ ai_gen_id }}" aria-label="Account">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
          <circle cx="12" cy="7" r="4"></circle>
        </svg>
      </a>

      {% if block.settings.show_wishlist %}
        <a href="/pages/wishlist" class="luxury-utility-btn-{{ ai_gen_id }}" aria-label="Wishlist">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
          </svg>
        </a>
      {% endif %}

      <a href="/cart" class="luxury-utility-btn-{{ ai_gen_id }}" aria-label="Cart">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"></path>
          <line x1="3" y1="6" x2="21" y2="6"></line>
          <path d="M16 10a4 4 0 0 1-8 0"></path>
        </svg>
        {% if cart.item_count > 0 %}
          <span class="luxury-cart-counter-{{ ai_gen_id }}">{{ cart.item_count }}</span>
        {% endif %}
      </a><button class="luxury-mobile-toggle-{{ ai_gen_id }}" data-mobile-toggle aria-label="Menu">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <line x1="3" y1="6" x2="21" y2="6"></line>
          <line x1="3" y1="12" x2="21" y2="12"></line>
          <line x1="3" y1="18" x2="21" y2="18"></line>
        </svg>
      </button>
    </div>
  </div>

  <div class="luxury-mobile-menu-{{ ai_gen_id }}" data-mobile-menu>
    <div class="luxury-mobile-header-{{ ai_gen_id }}">
      <div class="luxury-logo-{{ ai_gen_id }}">
        {% if block.settings.logo_image %}
          <a href="/">
            <img
              src="{{ block.settings.logo_image | image_url: width: 200 }}"
              alt="{{ shop.name }}"
              width="200"
              height="67"
              loading="lazy"
            >
          </a>
        {% else %}
          <a href="/">{{ block.settings.logo_text | default: shop.name }}</a>
        {% endif %}
      </div>
      <button class="luxury-mobile-close-{{ ai_gen_id }}" data-mobile-close aria-label="Close menu">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <line x1="18" y1="6" x2="6" y2="18"></line>
          <line x1="6" y1="6" x2="18" y2="18"></line>
        </svg>
      </button>
    </div>
    <nav class="luxury-mobile-nav-{{ ai_gen_id }}">
      {% for i in (1..5) %}
        {% assign menu_text_key = 'menu_' | append: i | append: '_text' %}
        {% assign menu_link_key = 'menu_' | append: i | append: '_link' %}
        {% assign menu_enabled_key = 'menu_' | append: i | append: '_enabled' %}

        {% if block.settings[menu_enabled_key] and block.settings[menu_text_key] != blank %}
          <a href="{{ block.settings[menu_link_key] }}">{{ block.settings[menu_text_key] }}</a>
        {% endif %}
      {% endfor %}
    </nav>
  </div>
</luxury-header-{{ ai_gen_id }}>

<script>
(function() {
  class LuxuryHeader{{ ai_gen_id }} extends HTMLElement {
    constructor() {
      super();
      this.scrollThreshold = 50;
      this.isScrolled = false;
    }

    connectedCallback() {
      this.header = this;
      this.mobileMenu = this.querySelector('[data-mobile-menu]');
      this.setupScrollEffect();
      this.setupMobileMenu();
      this.setupCartCounter();
      this.setupSearch();
    }

    setupScrollEffect() {
      let ticking = false;
      
      const updateHeader = () => {
        const scrollY = window.scrollY;
        const shouldBeScrolled = scrollY > this.scrollThreshold;
        
        if (shouldBeScrolled !== this.isScrolled) {
          this.isScrolled = shouldBeScrolled;
          this.header.classList.toggle('scrolled', this.isScrolled);
        }
        ticking = false;
      };

      window.addEventListener('scroll', () => {
        if (!ticking) {
          requestAnimationFrame(updateHeader);
          ticking = true;
        }
      }, { passive: true });
    }

    setupSearch() {
      const searchToggle = this.querySelector('[data-search-toggle]');
      
      searchToggle.addEventListener('click', () => {
        window.location.href = '/search';
      });
    }

    setupMobileMenu() {
      const mobileToggle = this.querySelector('[data-mobile-toggle]');
      const mobileClose = this.querySelector('[data-mobile-close]');

      mobileToggle.addEventListener('click', () => {
        this.openMobileMenu();
      });

      mobileClose.addEventListener('click', () => {
        this.closeMobileMenu();
      });

      this.mobileMenu.addEventListener('click', (e) => {
        if (e.target === this.mobileMenu) {
          this.closeMobileMenu();
        }
      });document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && this.mobileMenu.classList.contains('active')) {
          this.closeMobileMenu();
        }
      });
    }

    openMobileMenu() {
      this.mobileMenu.classList.add('active');
      document.body.style.overflow = 'hidden';document.body.style.position = 'fixed';
      document.body.style.width = '100%';
    }

    closeMobileMenu() {
      this.mobileMenu.classList.remove('active');
      document.body.style.overflow = '';
      document.body.style.position = '';
      document.body.style.width = '';
    }

    setupCartCounter() {document.addEventListener('cart:updated', () => {
        this.updateCartCounter();
      });

      fetch('/cart.js')
        .then(response => response.json())
        .then(cart => {
          this.updateCartDisplay(cart);
        })
        .catch(() => {});
    }

    updateCartCounter() {
      fetch('/cart.js')
        .then(response => response.json())
        .then(cart => {
          this.updateCartDisplay(cart);
        })
        .catch(() => {});
    }

    updateCartDisplay(cart) {
      const counter = this.querySelector('.luxury-cart-counter-{{ ai_gen_id }}');
      const cartBtn = this.querySelector('a[href="/cart"]');
      
      if (cart.item_count > 0) {
        if (counter) {
          counter.textContent = cart.item_count;counter.style.animation = 'none';
          counter.offsetHeight;
          counter.style.animation = 'luxury-cart-pulse-{{ ai_gen_id }} 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
        } else {
          const newCounter = document.createElement('span');
          newCounter.className = 'luxury-cart-counter-{{ ai_gen_id }}';
          newCounter.textContent = cart.item_count;
          cartBtn.appendChild(newCounter);
        }
      } else if (counter) {
        counter.remove();
      }
    }
  }

  customElements.define('luxury-header-{{ ai_gen_id }}', LuxuryHeader{{ ai_gen_id }});
})();
</script>

{% schema %}
{
  "name": "Luxury header",
  "tag": null,
  "settings": [
    {
      "type": "header",
      "content": "Logo"
    },
    {
      "type": "image_picker",
      "id": "logo_image",
      "label": "Logo image"
    },
    {
      "type": "text",
      "id": "logo_text",
      "label": "Logo text",
      "default": "LUXURY"
    },
    {
      "type": "font_picker",
      "id": "logo_font",
      "label": "Logo font",
      "default": "playfair_display_n4"
    },
    {
      "type": "range",
      "id": "logo_size",
      "min": 20,
      "max": 60,
      "step": 2,
      "unit": "px",
      "label": "Logo text size",
      "default": 36
    },
    {
      "type": "range",
      "id": "logo_height",
      "min": 30,
      "max": 100,
      "step": 5,
      "unit": "px",
      "label": "Logo image height",
      "default": 60
    },
    {
      "type": "range",
      "id": "logo_letter_spacing",
      "min": 0,
      "max": 5,
      "step": 0.5,
      "unit": "px",
      "label": "Logo letter spacing",
      "default": 2
    },
    {
      "type": "header",
      "content": "Header style"
    },
    {
      "type": "range",
      "id": "header_height",
      "min": 60,
      "max": 120,
      "step": 5,
      "unit": "px",
      "label": "Header height",
      "default": 90
    },
    {
      "type": "range",
      "id": "container_width",
      "min": 1200,
      "max": 1600,
      "step": 50,
      "unit": "px",
      "label": "Container width",
      "default": 1400
    },
    {
      "type": "range",
      "id": "container_padding",
      "min": 15,
      "max": 50,
      "step": 5,
      "unit": "px",
      "label": "Container padding",
      "default": 30
    },
    {
      "type": "color",
      "id": "header_background",
      "label": "Background color",
      "default": "#FFFFFF"
    },
    {
      "type": "color",
      "id": "primary_color",
      "label": "Primary color",
      "default": "#2C2C2C"
    },
    {
      "type": "color",
      "id": "accent_color",
      "label": "Accent color",
      "default": "#D4AF37"
    },
    {
      "type": "header",
      "content": "Navigation"
    },
    {
      "type": "range",
      "id": "nav_font_size",
      "min": 11,
      "max": 16,
      "step": 1,
      "unit": "px",
      "label": "Navigation font size",
      "default": 13
    },
    {
      "type": "range",
      "id": "nav_font_weight",
      "min": 400,
      "max": 700,
      "step": 100,
      "label": "Navigation font weight",
      "default": 500
    },
    {
      "type": "range",
      "id": "nav_letter_spacing",
      "min": 0,
      "max": 3,
      "step": 0.5,
      "unit": "px",
      "label": "Navigation letter spacing",
      "default": 1
    },
    {
      "type": "range",
      "id": "nav_spacing",
      "min": 10,
      "max": 50,
      "step": 5,
      "unit": "px",
      "label": "Navigation spacing",
      "default": 25
    },
    {
      "type": "range",
      "id": "nav_padding",
      "min": 8,
      "max": 20,
      "step": 2,
      "unit": "px",
      "label": "Navigation padding",
      "default": 12
    },
    {
      "type": "header",
      "content": "Utility icons"
    },
    {
      "type": "checkbox",
      "id": "show_wishlist",
      "label": "Show wishlist icon",
      "default": true
    },
    {
      "type": "range",
      "id": "icon_size",
      "min": 16,
      "max": 28,
      "step": 2,
      "unit": "px",
      "label": "Icon size",
      "default": 20
    },
    {
      "type": "range",
      "id": "utility_spacing",
      "min": 10,
      "max": 30,
      "step": 5,
      "unit": "px",
      "label": "Utility spacing",
      "default": 20
    },
    {
      "type": "range",
      "id": "utility_padding",
      "min": 8,
      "max": 16,
      "step": 2,
      "unit": "px",
      "label": "Utility padding",
      "default": 12
    },
    {
      "type": "range",
      "id": "counter_size",
      "min": 16,
      "max": 24,
      "step": 2,
      "unit": "px",
      "label": "Cart counter size",
      "default": 20
    },
    {
      "type": "range",
      "id": "counter_font_size",
      "min": 10,
      "max": 14,
      "step": 1,
      "unit": "px",
      "label": "Cart counter font size",
      "default": 11
    },
    {
      "type": "header",
      "content": "Menu1"
    },
    {
      "type": "checkbox",
      "id": "menu_1_enabled",
      "label": "Enable menu item",
      "default": true
    },
    {
      "type": "text",
      "id": "menu_1_text",
      "label": "Menu text",
      "default": "Shop"
    },
    {
      "type": "url",
      "id": "menu_1_link",
      "label": "Menu link"
    },
    {
      "type": "header",
      "content": "Menu 2"
    },
    {
      "type": "checkbox",
      "id": "menu_2_enabled",
      "label": "Enable menu item",
      "default": true
    },
    {
      "type": "text",
      "id": "menu_2_text",
      "label": "Menu text",
      "default": "Collections"
    },
    {
      "type": "url",
      "id": "menu_2_link",
      "label": "Menu link"
    },
    {
      "type": "header",
      "content": "Menu 3"
    },
    {
      "type": "checkbox",
      "id": "menu_3_enabled",
      "label": "Enable menu item",
      "default": true
    },
    {
      "type": "text",
      "id": "menu_3_text",
      "label": "Menu text",
      "default": "About"
    },
    {
      "type": "url",
      "id": "menu_3_link",
      "label": "Menu link"
    },
    {
      "type": "header",
      "content": "Menu 4"
    },
    {
      "type": "checkbox",
      "id": "menu_4_enabled",
      "label": "Enable menu item",
      "default": true
    },
    {
      "type": "text",
      "id": "menu_4_text",
      "label": "Menu text",
      "default": "Journal"
    },
    {
      "type": "url",
      "id": "menu_4_link",
      "label": "Menu link"
    },
    {
      "type": "header",
      "content": "Menu 5"
    },
    {
      "type": "checkbox",
      "id": "menu_5_enabled",
      "label": "Enable menu item",
      "default": true
    },
    {
      "type": "text",
      "id": "menu_5_text",
      "label": "Menu text",
      "default": "Contact"
    },
    {
      "type": "url",
      "id": "menu_5_link",
      "label": "Menu link"
    }
  ],
  "presets": [
    {
      "name": "Luxury header"
    }
  ]
}
{% endschema %}